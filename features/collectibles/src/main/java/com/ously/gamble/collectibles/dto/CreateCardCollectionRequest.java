package com.ously.gamble.collectibles.dto;

import com.ously.gamble.collectibles.validation.EndDateAfterStartDate;
import com.ously.gamble.collectibles.validation.UniqueCollectionName;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;

//@EndDateAfterStartDate
//@UniqueCollectionName
public record CreateCardCollectionRequest(
        @NotBlank(message = "Collection name cannot be empty")
        @Size(max = 100, message = "Collection name cannot exceed 100 characters")
        String name,

        LocalDateTime startDate,

        LocalDateTime endDate,

        @Min(value = 0, message = "Sort order cannot be negative")
        @Max(value = 99, message = "Sort order cannot exceed 99")
        Integer sortOrder
) {}